import React, { useState, useEffect, useRef } from 'react'
import { use<PERSON>ara<PERSON>, <PERSON> } from 'react-router-dom'
import ReportModal from './ReportModal'

const Chat = ({ user }) => {
  const { connectionId } = useParams()
  const [messages, setMessages] = useState([])
  const [newMessage, setNewMessage] = useState('')
  const [loading, setLoading] = useState(true)
  const [sending, setSending] = useState(false)
  const [showReportModal, setShowReportModal] = useState(false)
  const [partnerUserId, setPartnerUserId] = useState(null)
  const messagesEndRef = useRef(null)

  useEffect(() => {
    fetchMessages()
    // Set up polling for new messages (in production, use WebSocket)
    const interval = setInterval(fetchMessages, 2000)
    return () => clearInterval(interval)
  }, [connectionId])

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const fetchMessages = async () => {
    try {
      const response = await fetch(`/api/chat/${connectionId}/messages?userId=${user.id}`)
      const data = await response.json()
      if (response.ok) {
        setMessages(data.messages || [])
        // Get partner user ID from messages
        if (data.messages && data.messages.length > 0) {
          const firstMessage = data.messages[0]
          const partnerId = firstMessage.senderId === user.id
            ? data.messages.find(m => m.senderId !== user.id)?.senderId
            : firstMessage.senderId
          setPartnerUserId(partnerId)
        }
      }
    } catch (error) {
      console.error('Error fetching messages:', error)
    } finally {
      setLoading(false)
    }
  }

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const handleSendMessage = async (e) => {
    e.preventDefault()
    if (!newMessage.trim() || sending) return

    setSending(true)
    try {
      const response = await fetch(`/api/chat/${connectionId}/message`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          senderId: user.id,
          message: newMessage.trim()
        }),
      })

      if (response.ok) {
        setNewMessage('')
        fetchMessages() // Refresh messages
      }
    } catch (error) {
      console.error('Error sending message:', error)
    } finally {
      setSending(false)
    }
  }

  const formatTime = (timestamp) => {
    const date = new Date(timestamp)
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p className="text-comfort-600">Loading chat...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-warm-50 via-comfort-50 to-primary-50 flex flex-col">
      {/* Report Modal */}
      <ReportModal
        isOpen={showReportModal}
        onClose={() => setShowReportModal(false)}
        reportedUserId={partnerUserId}
        reporterId={user.id}
      />
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-comfort-200 p-4">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Link
              to="/dashboard"
              className="p-2 hover:bg-comfort-100 rounded-lg transition-colors duration-200"
            >
              <svg className="w-5 h-5 text-comfort-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </Link>
            <div>
              <h1 className="font-semibold text-gray-800">💬 Chat</h1>
              <p className="text-xs text-comfort-600">Safe space conversation</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowReportModal(true)}
              className="p-2 hover:bg-red-100 rounded-lg transition-colors duration-200 text-red-600"
              title="Report User"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </button>
          </div>
        </div>
      </header>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="max-w-4xl mx-auto">
          {messages.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-4xl mb-4">💝</div>
              <h3 className="font-semibold text-gray-800 mb-2">Start your conversation</h3>
              <p className="text-comfort-600 text-sm">
                Say hello and share what's on your mind. This is a safe space.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {messages.map((message, index) => {
                const isOwn = message.senderId === user.id
                const showTime = index === 0 || 
                  new Date(message.timestamp).getTime() - new Date(messages[index - 1].timestamp).getTime() > 300000 // 5 minutes

                return (
                  <div key={message.id}>
                    {showTime && (
                      <div className="text-center text-xs text-comfort-500 my-4">
                        {formatTime(message.timestamp)}
                      </div>
                    )}
                    <div className={`flex ${isOwn ? 'justify-end' : 'justify-start'}`}>
                      <div
                        className={`chat-bubble p-3 ${
                          isOwn ? 'chat-bubble sent' : 'chat-bubble received'
                        }`}
                      >
                        <p className="text-sm leading-relaxed">{message.message}</p>
                      </div>
                    </div>
                  </div>
                )
              })}
              <div ref={messagesEndRef} />
            </div>
          )}
        </div>
      </div>

      {/* Message Input */}
      <div className="bg-white/80 backdrop-blur-sm border-t border-comfort-200 p-4">
        <div className="max-w-4xl mx-auto">
          <form onSubmit={handleSendMessage} className="flex space-x-3">
            <input
              type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder="Type your message..."
              className="flex-1 px-4 py-3 rounded-xl border border-comfort-200 focus:border-primary-400 focus:ring-2 focus:ring-primary-100 transition-all duration-200 bg-white/70 backdrop-blur-sm"
              disabled={sending}
            />
            <button
              type="submit"
              disabled={!newMessage.trim() || sending}
              className={`px-6 py-3 rounded-xl font-medium transition-all duration-200 ${
                newMessage.trim() && !sending
                  ? 'bg-primary-500 hover:bg-primary-600 text-white'
                  : 'bg-comfort-200 text-comfort-500 cursor-not-allowed'
              }`}
            >
              {sending ? (
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              ) : (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
              )}
            </button>
          </form>
          
          {/* Safety reminder */}
          <div className="mt-3 text-center">
            <p className="text-xs text-comfort-500">
              💕 Remember to be kind and respectful. Report any inappropriate behavior.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Chat
