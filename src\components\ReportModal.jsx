import React, { useState } from 'react'

const ReportModal = ({ isOpen, onClose, reportedUserId, reporterId }) => {
  const [reason, setReason] = useState('')
  const [description, setDescription] = useState('')
  const [loading, setLoading] = useState(false)
  const [submitted, setSubmitted] = useState(false)

  const reportReasons = [
    'Inappropriate messages',
    'Harassment or bullying',
    'Spam or promotional content',
    'Threatening behavior',
    'Inappropriate requests',
    'Fake profile',
    'Other safety concern'
  ]

  const handleSubmit = async (e) => {
    e.preventDefault()
    if (!reason) return

    setLoading(true)
    try {
      const response = await fetch('/api/report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reporterId,
          reportedUserId,
          reason,
          description
        }),
      })

      if (response.ok) {
        setSubmitted(true)
        setTimeout(() => {
          onClose()
          setSubmitted(false)
          setReason('')
          setDescription('')
        }, 2000)
      }
    } catch (error) {
      console.error('Error submitting report:', error)
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full p-6">
        {submitted ? (
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">Report Submitted</h3>
            <p className="text-comfort-600 text-sm">
              Thank you for helping keep our community safe. We'll review this report promptly.
            </p>
          </div>
        ) : (
          <>
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h2 className="text-xl font-display font-bold text-gray-800 mb-2">
                Report User
              </h2>
              <p className="text-comfort-600 text-sm">
                Help us maintain a safe environment for everyone
              </p>
            </div>

            <form onSubmit={handleSubmit}>
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  What happened?
                </label>
                <div className="space-y-2">
                  {reportReasons.map(reportReason => (
                    <button
                      key={reportReason}
                      type="button"
                      onClick={() => setReason(reportReason)}
                      className={`block w-full text-left p-3 rounded-xl border transition-all duration-200 ${
                        reason === reportReason
                          ? 'border-red-400 bg-red-50 text-red-800'
                          : 'border-comfort-200 hover:border-red-300 hover:bg-red-50'
                      }`}
                    >
                      {reportReason}
                    </button>
                  ))}
                </div>
              </div>

              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Additional details (optional)
                </label>
                <textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Please provide any additional context that might help us understand the situation..."
                  className="w-full px-4 py-3 rounded-xl border border-comfort-200 focus:border-red-400 focus:ring-2 focus:ring-red-100 transition-all duration-200 bg-white/70 backdrop-blur-sm resize-none"
                  rows={4}
                />
              </div>

              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={onClose}
                  className="btn-secondary flex-1"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={!reason || loading}
                  className={`flex-1 py-3 px-6 rounded-xl font-medium transition-all duration-200 ${
                    reason && !loading
                      ? 'bg-red-500 hover:bg-red-600 text-white'
                      : 'bg-comfort-200 text-comfort-500 cursor-not-allowed'
                  }`}
                >
                  {loading ? 'Submitting...' : 'Submit Report'}
                </button>
              </div>
            </form>

            <div className="mt-6 p-4 bg-comfort-50 rounded-xl">
              <p className="text-sm text-comfort-700 text-center">
                <strong>Your safety matters.</strong> All reports are reviewed by our team. 
                For immediate danger, please contact local emergency services.
              </p>
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default ReportModal
