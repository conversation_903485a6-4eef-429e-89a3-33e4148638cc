# HeartConnect - Emotional Matchmaking App 💝

A safe, anonymous web application designed specifically for lonely and depressed women (including older women/aunties) to find meaningful emotional connections with understanding men. Built with React.js, Express.js API, and Tailwind CSS.

## 🌟 Features

### Core Functionality
- **Anonymous Registration**: No personal information required, completely anonymous
- **Emotional State Matching**: Connect based on shared feelings and emotional needs
- **Interest-Based Pairing**: Match users with similar hobbies and interests
- **Gender-Specific Matching**: Women are paired exclusively with men
- **Daily Connection Limits**: Maximum 2 connections per day to ensure quality interactions
- **Real-time Chat**: Simple, safe 1-on-1 messaging system
- **Daily Mood Check-ins**: Track emotional state for better matching

### Safety Features
- **User Reporting System**: Report inappropriate behavior
- **Content Moderation**: Built-in safety guidelines
- **Anonymous Profiles**: No personal information shared
- **Limited User Base**: Maximum 300 users for intimate community feel
- **Crisis Resources**: Links to mental health support services

### Design Philosophy
- **Warm, Soft UI**: Calming colors and gentle design for emotional comfort
- **Minimalist Interface**: Clean, distraction-free experience
- **Mobile-Responsive**: Works seamlessly on all devices
- **Accessibility**: Designed with emotional sensitivity in mind

## 🚀 Quick Start

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Installation

1. **Clone or download the project**
   ```bash
   cd emotional-matchmaking-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the API server**
   ```bash
   npm run server
   # or
   node server/index.js
   ```

4. **Start the React development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   - Frontend: http://localhost:3000
   - API: http://localhost:5000

## 📱 How to Use

### For New Users
1. **Visit the Welcome Page**: See available spots and app statistics
2. **Register Anonymously**: 
   - Select your emotional reasons (lonely, empty, need someone to talk to, etc.)
   - Choose interests (poetry, anime, tech, heartbreak, etc.)
   - Specify gender and age range
3. **Complete Daily Check-in**: Share your current mood
4. **Find Matches**: Browse compatible connections based on shared interests and emotions
5. **Start Conversations**: Connect with up to 2 people per day
6. **Chat Safely**: Engage in meaningful 1-on-1 conversations

### Daily Workflow
- **Morning Check-in**: Update your emotional state
- **Browse Matches**: Find new compatible connections
- **Chat**: Continue conversations with existing connections
- **Evening Reflection**: Optional mood update

## 🛠 Technical Architecture

### Frontend (React.js)
- **Framework**: React 18 with Vite
- **Styling**: Tailwind CSS with custom warm color palette
- **Routing**: React Router for navigation
- **State Management**: React hooks and local storage
- **Components**: Modular, reusable UI components

### Backend (Express.js API)
- **Server**: Express.js with CORS enabled
- **Database**: In-memory storage (easily replaceable with Firebase/MongoDB)
- **Authentication**: Anonymous user system
- **Real-time**: Polling-based chat (upgradeable to WebSocket)

### Key API Endpoints
```
GET  /api/stats                    - App statistics
POST /api/register                 - User registration
GET  /api/user/:id                 - User profile
POST /api/user/:id/mood           - Update mood
GET  /api/user/:id/matches        - Find matches
POST /api/connect                  - Create connection
GET  /api/user/:id/connections    - User's connections
POST /api/chat/:id/message        - Send message
GET  /api/chat/:id/messages       - Get messages
POST /api/report                   - Report user
```

## 🎨 Design System

### Color Palette
- **Primary**: Warm oranges (#ed7c4a, #de6135) for comfort and warmth
- **Secondary**: Soft blues (#0ea5e9, #0284c7) for trust and calm
- **Warm**: Peachy tones (#f4b370, #f8cc9b) for emotional comfort
- **Comfort**: Muted browns (#bfa094, #d2bab0) for grounding

### Typography
- **Display Font**: Poppins for headings
- **Body Font**: Inter for readability
- **Emotional Tone**: Gentle, supportive language throughout

## 🔒 Privacy & Safety

### Data Protection
- **No Personal Information**: Only emotional state and interests stored
- **Anonymous IDs**: Users identified by random UUIDs
- **Local Storage**: User data stored locally, not on servers
- **No Tracking**: No analytics or user behavior tracking

### Safety Measures
- **Daily Limits**: Prevents overwhelming interactions
- **Reporting System**: Easy-to-use reporting for inappropriate behavior
- **Crisis Resources**: Mental health support information readily available
- **Content Guidelines**: Clear community standards

## 🌍 Target Audience

### Primary Users
- **Lonely Women**: Seeking emotional connection and understanding
- **Depressed Individuals**: Looking for supportive conversations
- **Older Women (Aunties)**: Wanting meaningful relationships
- **Isolated People**: Needing someone to talk to

### Age Groups
- **18-25**: Young adults facing life transitions
- **26-35**: Career and relationship challenges
- **36-45**: Mid-life emotional needs
- **46+**: Mature women seeking companionship

## 🚧 Current Limitations & Future Enhancements

### Current Limitations
- **In-Memory Storage**: Data resets on server restart
- **Polling-Based Chat**: Not real-time (2-second intervals)
- **No Image Sharing**: Text-only conversations
- **Basic Moderation**: Manual reporting system

### Planned Enhancements
- **Firebase Integration**: Persistent data storage
- **WebSocket Chat**: Real-time messaging
- **AI Moderation**: Automated content filtering
- **Voice Messages**: Audio communication option
- **Group Support**: Small group conversations
- **Professional Integration**: Licensed therapist connections

## 📞 Crisis Resources

If you or someone you know is experiencing severe depression or suicidal thoughts:

- **US National Suicide Prevention Lifeline**: 988
- **Crisis Text Line**: Text HOME to 741741
- **International**: Visit befrienders.org
- **Emergency**: Call your local emergency number

## 🤝 Contributing

This is a compassionate project aimed at helping people in emotional distress. Contributions should maintain the app's supportive, safe environment.

### Development Guidelines
- **Emotional Sensitivity**: All features should prioritize user emotional well-being
- **Privacy First**: Never compromise user anonymity
- **Safety Focus**: Every feature should enhance user safety
- **Inclusive Design**: Consider diverse emotional and cultural needs

## 📄 License

This project is created for humanitarian purposes to help lonely and depressed individuals find emotional support and meaningful connections.

---

**Remember**: This app is for emotional support and friendship. If you're experiencing severe depression, please also consider professional mental health services. You are not alone. 💕
