import React, { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import MoodCheckIn from './MoodCheckIn'
import MatchList from './MatchList'
import ReportModal from './ReportModal'

const Dashboard = ({ user, onLogout }) => {
  const [userProfile, setUserProfile] = useState(null)
  const [showMoodCheckIn, setShowMoodCheckIn] = useState(false)
  const [showReportModal, setShowReportModal] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchUserProfile()
  }, [user.id])

  const fetchUserProfile = async () => {
    try {
      const response = await fetch(`/api/user/${user.id}`)
      const data = await response.json()
      setUserProfile(data)
      
      // Check if mood check-in is needed (if no mood today)
      const lastMoodUpdate = data.currentMood?.updatedAt
      const today = new Date().toDateString()
      const moodToday = lastMoodUpdate && new Date(lastMoodUpdate).toDateString() === today
      
      if (!moodToday) {
        setShowMoodCheckIn(true)
      }
    } catch (error) {
      console.error('Error fetching user profile:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleMoodUpdate = (moodData) => {
    setUserProfile(prev => ({
      ...prev,
      currentMood: moodData
    }))
    setShowMoodCheckIn(false)
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p className="text-comfort-600">Loading your dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-warm-50 via-comfort-50 to-primary-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-comfort-200 sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-primary-400 to-primary-600 rounded-full flex items-center justify-center">
              <span className="text-lg text-white">💝</span>
            </div>
            <div>
              <h1 className="font-display font-bold text-lg gradient-text">HeartConnect</h1>
              <p className="text-xs text-comfort-600">Your safe space</p>
            </div>
          </div>
          
          <button
            onClick={onLogout}
            className="text-comfort-600 hover:text-primary-600 text-sm font-medium transition-colors duration-200"
          >
            Leave
          </button>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Mood Check-in Modal */}
        {showMoodCheckIn && (
          <MoodCheckIn
            userId={user.id}
            onMoodUpdate={handleMoodUpdate}
            onClose={() => setShowMoodCheckIn(false)}
          />
        )}

        {/* Report Modal */}
        <ReportModal
          isOpen={showReportModal}
          onClose={() => setShowReportModal(false)}
          reportedUserId={null}
          reporterId={user.id}
        />

        {/* Welcome Section */}
        <div className="card mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-display font-bold text-gray-800 mb-2">
                Welcome back 💕
              </h2>
              <p className="text-comfort-600">
                {userProfile?.currentMood 
                  ? `You're feeling ${userProfile.currentMood.mood} today`
                  : "How are you feeling today?"
                }
              </p>
            </div>
            <button
              onClick={() => setShowMoodCheckIn(true)}
              className="btn-secondary text-sm"
            >
              Update Mood
            </button>
          </div>

          {/* Connection Stats */}
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center p-4 bg-primary-50 rounded-xl">
              <div className="text-2xl font-bold text-primary-600">
                {userProfile?.remainingConnections || 0}
              </div>
              <div className="text-xs text-primary-700">Connections Left Today</div>
            </div>
            <div className="text-center p-4 bg-comfort-50 rounded-xl">
              <div className="text-2xl font-bold text-comfort-600">
                {userProfile?.interests?.length || 0}
              </div>
              <div className="text-xs text-comfort-700">Your Interests</div>
            </div>
            <div className="text-center p-4 bg-warm-50 rounded-xl">
              <div className="text-2xl font-bold text-warm-600">
                {userProfile?.emotionalReasons?.length || 0}
              </div>
              <div className="text-xs text-warm-700">Emotional Reasons</div>
            </div>
          </div>
        </div>

        {/* Your Profile */}
        <div className="card mb-8">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Your Profile</h3>
          
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Why you're here:</h4>
              <div className="flex flex-wrap gap-2">
                {userProfile?.emotionalReasons?.map(reason => (
                  <span key={reason} className="bg-primary-100 text-primary-700 px-3 py-1 rounded-full text-sm">
                    {reason}
                  </span>
                ))}
              </div>
            </div>
            
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Your interests:</h4>
              <div className="flex flex-wrap gap-2">
                {userProfile?.interests?.map(interest => (
                  <span key={interest} className="bg-comfort-100 text-comfort-700 px-3 py-1 rounded-full text-sm">
                    {interest}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Find Connections */}
        {userProfile && (
          <MatchList 
            userId={user.id} 
            remainingConnections={userProfile.remainingConnections}
          />
        )}

        {/* Safety & Support */}
        <div className="card mt-8">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Safety & Support</h3>
          <div className="grid grid-cols-2 gap-4">
            <button
              onClick={() => setShowReportModal(true)}
              className="p-4 bg-red-50 hover:bg-red-100 rounded-xl border border-red-200 transition-colors duration-200"
            >
              <div className="text-2xl mb-2">🚨</div>
              <div className="text-sm font-medium text-red-700">Report User</div>
            </button>
            <button className="p-4 bg-blue-50 hover:bg-blue-100 rounded-xl border border-blue-200 transition-colors duration-200">
              <div className="text-2xl mb-2">💬</div>
              <div className="text-sm font-medium text-blue-700">Get Help</div>
            </button>
          </div>
          
          <div className="mt-4 p-4 bg-comfort-50 rounded-xl">
            <p className="text-sm text-comfort-700">
              <strong>Remember:</strong> This is a safe space for emotional support. 
              If someone makes you uncomfortable, please report them immediately.
            </p>
          </div>
        </div>

        {/* Crisis Resources */}
        <div className="mt-8 p-4 bg-gradient-to-r from-primary-50 to-warm-50 rounded-xl border border-primary-200">
          <h4 className="font-semibold text-primary-800 mb-2">Need immediate help?</h4>
          <p className="text-sm text-primary-700 mb-3">
            If you're having thoughts of self-harm, please reach out for professional support:
          </p>
          <div className="space-y-1 text-sm">
            <p className="text-primary-600">🇺🇸 National Suicide Prevention Lifeline: 988</p>
            <p className="text-primary-600">🇺🇸 Crisis Text Line: Text HOME to 741741</p>
            <p className="text-primary-600">🌍 International: befrienders.org</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
